<?php

namespace app\api\controller;

use app\BaseController;
use app\api\model\Sheet as SheetModel;
use app\api\model\SheetColumn as SheetColumnModel;

class SheeColumn extends BaseController
{
    // 字段类型映射
    private $typeMap = [
        'string' => '文本',
        'member' => '人员',
        'date' => '日期',
        'file' => '附件',
        'radio' => '单选',
        'check' => '多选',
        'dingshi' => '定时',
        'dingshidate' => '定时截止日期',
        'isdingshi' => '是否开启定时任务',
        'number' => '数字',
        'flow' => '流程',
        'flowNode' => '流程步骤'
    ];

    /**
     * 获取表单列表（用于字段管理）
     * @return \think\response\Json
     */
    public function index()
    {
        try {
            // 获取筛选参数
            $filter = [
                'filter_name' => $this->request->param('filter_name', ''),
                'filter_one_sheet' => $this->request->param('filter_one_sheet', ''),
                'filter_second_sheet' => $this->request->param('filter_second_sheet', ''),
                'filter_date_start' => $this->request->param('filter_date_start', ''),
                'filter_date_end' => $this->request->param('filter_date_end', ''),
                'sort' => $this->request->param('sort', 'createTime'),
                'order' => $this->request->param('order', 'DESC'),
                'page' => (int)$this->request->param('page', 1),
                'limit' => (int)$this->request->param('limit', 15),
            ];

            // 实例化表单模型
            $sheetModel = new SheetModel();

            // 获取表单列表数据（只获取有字段的表单，即type=1的表单）
            $result = $sheetModel->getSheets($filter, 1);

            // 处理表单数据
            $sheets = [];
            foreach ($result->items() as $sheet) {
                $sheets[] = [
                    'id' => $sheet['sheet_id'],
                    'name' => $sheet['name'],
                    'entry' => date('Y-m-d H:i:s', $sheet['createTime']),
                    'is_one' => $sheet['cate'] == 0 ? '是' : '',
                    'is_two' => $sheet['type'] == 1 ? '是' : '',
                    'is_three' => $sheet['type'] == 2 ? '是' : '',
                    'cate' => $sheet['cate'],
                    'type' => $sheet['type'],
                    'flow_id' => $sheet['flow_id'],
                ];
            }

            // 获取一级表单列表
            $sheetOneList = $sheetModel->getOneSheet();

            // 构建返回数据
            $data = [
                'sheets' => $sheets,
                'pagination' => [
                    'total' => $result->total(),
                    'per_page' => $result->listRows(),
                    'current_page' => $result->currentPage(),
                    'last_page' => $result->lastPage(),
                ],
                'filters' => [
                    'filter_name' => $filter['filter_name'],
                    'filter_one_sheet' => $filter['filter_one_sheet'],
                    'filter_second_sheet' => $filter['filter_second_sheet'],
                    'filter_date_start' => $filter['filter_date_start'],
                    'filter_date_end' => $filter['filter_date_end'],
                ],
                'sort' => [
                    'field' => $filter['sort'],
                    'order' => $filter['order'],
                ],
                'sheetOneList' => $sheetOneList,
            ];

            return $this->success($data, '获取表单列表成功');

        } catch (\Exception $e) {
            return $this->error('获取表单列表失败：' . $e->getMessage());
        }
    }

    /**
     * 获取字段详情列表
     * @return \think\response\Json
     */
    public function detail()
    {
        try {
            // 获取表单ID
            $sheetId = (int)$this->request->param('sheet_id', 0);
            if (!$sheetId) {
                return $this->error('表单ID不能为空');
            }

            // 获取筛选参数
            $filter = [
                'filter_name' => $this->request->param('filter_name', ''),
                'filter_date_start' => $this->request->param('filter_date_start', ''),
                'filter_date_end' => $this->request->param('filter_date_end', ''),
                'sort' => $this->request->param('sort', 'createTime'),
                'order' => $this->request->param('order', 'DESC'),
                'page' => (int)$this->request->param('page', 1),
                'limit' => (int)$this->request->param('limit', 15),
            ];

            // 实例化字段模型
            $columnModel = new SheetColumnModel();

            // 获取字段列表数据
            $result = $columnModel->getColumns($filter, $sheetId);

            // 处理字段数据
            $columns = [];
            foreach ($result->items() as $column) {
                $columns[] = [
                    'id' => $column['column_id'],
                    'name' => $column['name'],
                    'type' => $column['type'],
                    'type_name' => $this->typeMap[$column['type']] ?? $column['type'],
                    'sort' => $column['sort'],
                    'entry' => date('Y-m-d H:i:s', $column['createTime']),
                    'as_name' => $column['asName'],
                    'is_show' => $column['isShow'],
                ];
            }

            // 构建返回数据
            $data = [
                'sheet_id' => $sheetId,
                'columns' => $columns,
                'pagination' => [
                    'total' => $result->total(),
                    'per_page' => $result->listRows(),
                    'current_page' => $result->currentPage(),
                    'last_page' => $result->lastPage(),
                ],
                'filters' => [
                    'filter_name' => $filter['filter_name'],
                    'filter_date_start' => $filter['filter_date_start'],
                    'filter_date_end' => $filter['filter_date_end'],
                ],
                'sort' => [
                    'field' => $filter['sort'],
                    'order' => $filter['order'],
                ],
                'type_map' => $this->typeMap,
            ];

            return $this->success($data, '获取字段列表成功');

        } catch (\Exception $e) {
            return $this->error('获取字段列表失败：' . $e->getMessage());
        }
    }

    /**
     * 添加字段
     * @return \think\response\Json
     */
    public function add()
    {
        try {
            // 获取表单ID
            $sheetId = (int)$this->request->param('sheet_id', 0);
            if (!$sheetId) {
                return $this->error('表单ID不能为空');
            }

            // 如果是GET请求，返回表单数据
            if (!$this->request->isPost()) {
                return $this->getColumnFormData($sheetId);
            }

            // 获取POST数据
            $data = $this->request->post();

            // 数据验证
            $validateResult = $this->validateColumnForm($data, $sheetId);
            if ($validateResult !== true) {
                return $this->error($validateResult);
            }

            // 实例化字段模型
            $columnModel = new SheetColumnModel();

            // 添加字段
            $columnId = $columnModel->addColumn($data, $sheetId);

            if ($columnId) {
                return $this->success(['column_id' => $columnId], '字段添加成功');
            } else {
                return $this->error('字段添加失败');
            }

        } catch (\Exception $e) {
            return $this->error('字段添加失败：' . $e->getMessage());
        }
    }

    /**
     * 编辑字段
     * @return \think\response\Json
     */
    public function edit()
    {
        try {
            // 获取字段ID和表单ID
            $columnId = (int)$this->request->param('column_id', 0);
            $sheetId = (int)$this->request->param('sheet_id', 0);

            if (!$columnId) {
                return $this->error('字段ID不能为空');
            }
 
       

            // 如果是GET请求，返回表单数据
            if (!$this->request->isPost()) {
                return $this->getColumnFormData($sheetId, $columnId);
            }

                // 获取POST数据
            $data = $this->request->post();
            if (!$data['name']) {
                return $this->error('名称不能为空');
            }

        

     

            // 实例化字段模型
            $columnModel = new SheetColumnModel();

            // 编辑字段
            $result = $columnModel->editColumn($columnId, $data, $sheetId);

            if ($result) {
                return $this->success(['column_id' => $columnId], '字段编辑成功');
            } else {
                return $this->error('字段编辑失败');
            }

        } catch (\Exception $e) {
            return $this->error('字段编辑失败：' . $e->getMessage());
        }
    }

    /**
     * 删除字段
     * @return \think\response\Json
     */
    public function delete()
    {
        try {
            // 验证请求方法
            if (!$this->request->isPost()) {
                return $this->error('请求方法错误');
            }

            // 获取表单ID和要删除的字段ID
            $sheetId = (int)$this->request->param('sheet_id', 0);
            $selected = $this->request->param('selected', []);

            if (!$sheetId) {
                return $this->error('表单ID不能为空');
            }
            if (empty($selected)) {
                return $this->error('请选择要删除的字段');
            }

            // 实例化字段模型
            $columnModel = new SheetColumnModel();

            // 验证删除
            $validateResult = $columnModel->validateColumnDel($sheetId, $selected);
            if ($validateResult === '100') {
                return $this->error('至少要保留一个字段');
            } elseif ($validateResult === '300') {
                return $this->error('特殊字段"发起人","执行人员","标题","完成进度"不能删除');
            }

            // 删除字段
            $successCount = 0;
            foreach ($selected as $columnId) {
                if ($columnModel->deleteColumn($columnId)) {
                    $successCount++;
                }
               
            }

            if ($successCount > 0) {
                return $this->success(['deleted_count' => $successCount], '删除成功');
            } else {
                return $this->error('删除失败');
            }

        } catch (\Exception $e) {
            return $this->error('删除失败：' . $e->getMessage());
        }
    }

    /**
     * 获取字段表单数据（用于添加/编辑字段时）
     * @param int $sheetId 表单ID
     * @param int $columnId 字段ID（编辑时）
     * @return \think\response\Json
     */
    private function getColumnFormData($sheetId, $columnId = 0)
    {
        try {
            // 实例化字段模型
            $columnModel = new SheetColumnModel();

            $data = [
                'text_form' => $columnId ? '编辑字段' : '添加字段',
                'sheet_id' => $sheetId,
                'column_id' => $columnId,
                'name' => '',
                'type' => '',
                'type_name' => '',
                'sort' => 0,
                'radio_check' => [],
            ];

            // 如果是编辑，获取字段信息
            if ($columnId) {
                $columnInfo = $columnModel->getColumnInfo($columnId);
                if ($columnInfo) {
                    $data['name'] = $columnInfo['name'];
                    $data['type'] = $columnInfo['type'];
                    $data['type_name'] = $this->typeMap[$columnInfo['type']] ?? $columnInfo['type'];
                    $data['sort'] = $columnInfo['sort'];

                    // 如果是单选或多选字段，获取选项
                    if ($columnInfo['type'] == 'radio' || $columnInfo['type'] == 'check') {
                        $radioCheckList = $columnModel->getRadioCheck($columnId);
                        $data['radio_check'] = array_column($radioCheckList, 'title');
                    }
                }
            }

            // 获取字段类型列表
            $data['type_list'] = $this->typeMap;

            return $this->success($data, '获取字段表单数据成功');

        } catch (\Exception $e) {
            return $this->error('获取字段表单数据失败：' . $e->getMessage());
        }
    }

    /**
     * 验证字段表单数据
     * @param array $data 字段数据
     * @param int $sheetId 表单ID
     * @param int $columnId 字段ID（编辑时）
     * @return string|true 验证结果，true表示通过，字符串表示错误信息
     */
    private function validateColumnForm($data, $sheetId, $columnId = 0)
    {
        // 字段名称验证
        if (empty($data['name'])) {
            return '名称必填!';
        }

        // 字段类型验证
        if (empty($data['type'])) {
            return '类型必选!';
        }

        // 单选/多选选项验证
        if ($data['type'] == 'radio' || $data['type'] == 'check') {
            if (empty($data['radio_check'])) {
                return '没填写选择项!';
            }
        }

        // 实例化字段模型
        $columnModel = new SheetColumnModel();

        // 名称重复验证
        $validateResult = $columnModel->validateColumnName($data, $sheetId, $columnId);
        if ($validateResult != '200') {
            return '名称重复!';
        }

        return true;
    }

    /**
     * 获取二级分类（从Sheet控制器复用）
     * @return \think\response\Json
     */
    public function getSecondCate()
    {
        try {
            $filterOneSheet = $this->request->param('filter_one_sheet', '');
            $filterSecondSheet = $this->request->param('filter_second_sheet', '');
            $noall = $this->request->param('noall', false);

            // 实例化表单模型
            $sheetModel = new SheetModel();

            $options = [];

            if ($noall) {
                $options[] = ['value' => '', 'text' => ''];
            } else {
                $options[] = ['value' => '*', 'text' => '全部级别'];
            }

            if ($filterOneSheet == '' || $filterOneSheet == '*') {
                // 如果没有选择一级分类，只返回全部级别
            } else {
                $results = $sheetModel->getSheetById($filterOneSheet);
                foreach ($results as $result) {
                    $options[] = [
                        'value' => $result['sheet_id'],
                        'text' => $result['name'],
                        'selected' => $filterSecondSheet == $result['sheet_id']
                    ];
                }
            }

            return $this->success($options, '获取二级分类成功');

        } catch (\Exception $e) {
            return $this->error('获取二级分类失败：' . $e->getMessage());
        }
    }
}
