<?php

use function PHPSTORM_META\map;

class ControllerAdminSheet extends Controller
{
    private $error = array();
    private $type = [
        'string' => '文本',
        'member' => '人员',
        'date' => '日期',
        'file' => '附件',
        'radio' => '单选',
        'check' => '多选',
        'dingshi' => '定时',
        'dingshidate' => '定时截止日期',
        'isdingshi'=>'是否开启定时任务',
        'number'=>'数字',
        'flow'=>'流程',
        'flowNode'=>'流程步骤'
    ];


    public function getList()
    {
        $this->load->model('admin/sheet');
        $this->load->model('admin/sheet');

        if (isset($this->request->get['filter_name'])) {
            $filter_name = $this->request->get['filter_name'];
        } else {
            $filter_name = '';
        }

        if (isset($this->request->get['filter_status'])) {
            $filter_status = $this->request->get['filter_status'];
        } else {
            $filter_status = '';
        }

        if (isset($this->request->get['filter_one_sheet'])) {
            $filter_one_sheet = $this->request->get['filter_one_sheet'];
        } else {
            $filter_one_sheet = '';
        }

        if (isset($this->request->get['filter_second_sheet'])) {
            $filter_second_sheet = $this->request->get['filter_second_sheet'];
        } else {
            $filter_second_sheet = '';
        }

        if (isset($this->request->get['filter_date_start'])) {
            $filter_date_start = $this->request->get['filter_date_start'];
        } else {
            $filter_date_start = '';
        }

        if (isset($this->request->get['filter_date_end'])) {
            $filter_date_end = $this->request->get['filter_date_end'];
        } else {
            $filter_date_end = '';
        }

        if (isset($this->request->get['sort'])) {
            $sort = $this->request->get['sort'];
        } else {
            $sort = 'createTime';
        }

        if (isset($this->request->get['is_one'])) {
            $is_one = $this->request->get['is_one'];
        } else {
            $is_one = '';
        }

        if (isset($this->request->get['order'])) {
            $order = $this->request->get['order'];
        } else {
            $order = 'DESC';
        }


        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_status'])) {
            $url .= '&filter_status=' . urlencode(html_entity_decode($this->request->get['filter_status'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_one_sheet'])) {
            $url .= '&filter_one_sheet=' . urlencode(html_entity_decode($this->request->get['filter_one_sheet'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_second_sheet'])) {
            $url .= '&filter_second_sheet=' . urlencode(html_entity_decode($this->request->get['filter_second_sheet'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }

        if (isset($this->request->get['sort'])) {
            $url .= '&sort=' . $this->request->get['sort'];
        }

        if ($order == 'ASC') {
            $url .= '&order=DESC';
        } else {
            $url .= '&order=ASC';
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        $data['add'] = $this->url->link('admin/sheet/add', 'token=' . $this->session->data['token'] . $url);
        $data['admin_add_edit'] = $this->url->link('admin/sheet/admin_add_edit', 'token=' . $this->session->data['token'] );
        $data['delete'] = $this->url->link('admin/sheet/delete', 'token=' . $this->session->data['token'] . $url);
        $data['getSecondCate'] = $this->url->link('admin/sheet/getSecondCate', 'token=' . $this->session->data['token']);


        $data['sheetOneList'] = $this->model_admin_sheet->getOneSheet();

        $filter_data = array(
            'filter_name' => $filter_name,
            'is_one'=>$is_one,
            'filter_one_sheet' => $filter_one_sheet,
            'filter_second_sheet' => $filter_second_sheet,
            'filter_date_start' => $filter_date_start,
            'filter_date_end' => $filter_date_end,
            'sort' => $sort,
            'order'=> $order,
            'start' => ($page - 1) * $this->config->get('config_limit'),
            'limit' => $this->config->get('config_limit'),
            'filter_status' => $filter_status
        );


        $results = $this->model_admin_sheet->getSheets($filter_data);

        foreach ($results as $k => $result) {
            $data['sheet'][$k] = array(
                'id' => $result['sheet_id'],
                'name' => $result['name'],
                'entry' => date('Y-m-d H:i:s', $result['createTime']),
                'is_one' => $result['cate'] == 0 ? '是' : '',
                'is_two' => $result['type'] == 1 ? '是' : '',
                'is_three' => $result['type'] == 2 ? '是' : '',
                'edit' => $this->url->link('admin/sheet/edit', 'token=' . $this->session->data['token'] . '&sheet_id=' . $result['sheet_id'] . $url),
            );
        }
        $total = $this->model_admin_sheet->getTotalSheets($filter_data);

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }

        if (isset($this->session->data['success'])) {
            $data['success'] = $this->session->data['success'];
            unset($this->session->data['success']);
        } else {
            $data['success'] = '';
        }

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_status'])) {
            $url .= '&filter_status=' . urlencode(html_entity_decode($this->request->get['filter_status'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_one_sheet'])) {
            $url .= '&filter_one_sheet=' . urlencode(html_entity_decode($this->request->get['filter_one_sheet'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_second_sheet'])) {
            $url .= '&filter_second_sheet=' . urlencode(html_entity_decode($this->request->get['filter_second_sheet'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }

        if ($order == 'ASC') {
            $url .= '&order=DESC';
        } else {
            $url .= '&order=ASC';
        }

        // if (isset($this->request->get['page'])) {
        //     $url .= '&page=' . $this->request->get['page'];
        // }

        

        $data['sort_name'] = $this->url->link('admin/sheet/getList', 'token=' . $this->session->data['token'] . '&sort=name' . $url);
        $data['sort_is_one'] = $this->url->link('admin/sheet/getList', 'token=' . $this->session->data['token'] . '&sort=is_one' . $url);
        $data['sort_is_two'] = $this->url->link('admin/sheet/getList', 'token=' . $this->session->data['token'] . '&sort=is_two' . $url);
        $data['sort_is_three'] = $this->url->link('admin/sheet/getList', 'token=' . $this->session->data['token'] . '&sort=is_three' . $url);
        $data['sort_createTime'] = $this->url->link('admin/sheet/getList', 'token=' . $this->session->data['token'] . '&sort=createTime' . $url);


        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('admin/sheet/getList', 'token=' . $this->session->data['token'] . $url . '&page={page}');

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $data['filter_name'] = $filter_name;
        $data['filter_one_sheet'] = $filter_one_sheet;
        $data['filter_second_sheet'] = $filter_second_sheet;
        $data['filter_date_start'] = $filter_date_start;
        $data['filter_date_end'] = $filter_date_end;
        $data['filter_status'] = $filter_status;

        $data['nofilter'] = $this->url->link('admin/sheet/getList', 'token=' . $this->session->data['token']);

        $data['sort'] = $sort;
        $data['order'] = $order;

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('sheet/list.tpl', $data));
    }

    public function add()
    {
        $this->load->model('admin/sheet');
        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
            $this->model_admin_sheet->addSheet($this->request->post);

            $this->session->data['success'] = '添加成功';

            $url = '';

            if (isset($this->request->get['filter_name'])) {
                $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
            }

            if (isset($this->request->get['filter_one_sheet'])) {
                $url .= '&filter_one_sheet=' . urlencode(html_entity_decode($this->request->get['filter_one_sheet'], ENT_QUOTES, 'UTF-8'));
            }

            if (isset($this->request->get['filter_second_sheet'])) {
                $url .= '&filter_second_sheet=' . urlencode(html_entity_decode($this->request->get['filter_second_sheet'], ENT_QUOTES, 'UTF-8'));
            }

            if (isset($this->request->get['filter_date_start'])) {
                $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
            }

            if (isset($this->request->get['filter_date_end'])) {
                $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
            }

            if (isset($this->request->get['sort'])) {
                $url .= '&sort=' . $this->request->get['sort'];
            }


            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/sheet/getList', 'token=' . $this->session->data['token'] . $url));
        }

        $this->getForm();
    }

    public function edit()
    {
        $this->load->model('admin/sheet');
        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm($this->request->get['sheet_id'])) {

            $this->model_admin_sheet->editsheet($this->request->get['sheet_id'], $this->request->post);

            $this->session->data['success'] = $this->language->get('编辑成功');

            $url = '';

            if (isset($this->request->get['filter_name'])) {
                $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
            }

            if (isset($this->request->get['filter_one_sheet'])) {
                $url .= '&filter_one_sheet=' . urlencode(html_entity_decode($this->request->get['filter_one_sheet'], ENT_QUOTES, 'UTF-8'));
            }

            if (isset($this->request->get['filter_second_sheet'])) {
                $url .= '&filter_second_sheet=' . urlencode(html_entity_decode($this->request->get['filter_second_sheet'], ENT_QUOTES, 'UTF-8'));
            }

            if (isset($this->request->get['filter_date_start'])) {
                $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
            }

            if (isset($this->request->get['filter_date_end'])) {
                $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
            }

            if (isset($this->request->get['sort'])) {
                $url .= '&sort=' . $this->request->get['sort'];
            }


            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/sheet/getList', 'token=' . $this->session->data['token'] . $url));
        }

        $this->getForm();
    }

    public function delete()
    {
        $this->load->model('admin/sheet');

        if (isset($this->request->post['selected']) && $this->validateDelete($this->request->post['selected'])) {
            foreach ($this->request->post['selected'] as $sheet_id) {

                $this->model_admin_sheet->deletesheet($sheet_id);
            }


            $this->session->data['success'] = $this->language->get('text_delete_success');

            $url = '';

            if (isset($this->request->get['filter_name'])) {
                $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
            }

            if (isset($this->request->get['filter_department'])) {
                $url .= '&filter_department=' . urlencode(html_entity_decode($this->request->get['filter_department'], ENT_QUOTES, 'UTF-8'));
            }

            if (isset($this->request->get['filter_status'])) {
                $url .= '&filter_status=' . urlencode(html_entity_decode($this->request->get['filter_status'], ENT_QUOTES, 'UTF-8'));
            }

            if (isset($this->request->get['filter_date_start'])) {
                $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
            }

            if (isset($this->request->get['filter_date_end'])) {
                $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
            }



            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/sheet/getList', 'token=' . $this->session->data['token'] . $url));
        }

        $this->getList();
    }




    public function getSecondCate()
    {
        $filter_one_sheet = $this->request->get['filter_one_sheet']  ?? '';
        $filter_second_sheet = $this->request->get['filter_second_sheet'] ?? '';
        $this->load->model('admin/sheet');
        if (isset($this->request->get['noall'])) {
            $output = '<option value=""></option>';
        } else {
            $output = '<option value="*">全部级别</option>';
        }

        if ($filter_one_sheet == '' || $filter_one_sheet == '*') {
            $output = '<option value="*">全部级别</option>';
        } else {

            $results = $this->model_admin_sheet->getSheetById($filter_one_sheet);
            foreach ($results as $result) {
                $output .= '<option value="' . $result['sheet_id'] . '"';

                if ($filter_second_sheet == $result['sheet_id']) {
                    $output .= ' selected="selected"';
                }

                $output .= '>' . $result['name'] . '</option>';
            }

        }


        $this->response->setOutput($output);
    }


    protected function getForm()
    {
        $this->load->model('admin/sheet');
        $data['text_form'] = !isset($this->request->get['sheet_id']) ? $this->language->get('text_add') : $this->language->get('text_edit');

		if (isset($this->error['warning'])) {
			$data['warning'] = $this->error['warning'];
		} else {
			$data['warning'] = '';
		}

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_one_sheet'])) {
            $url .= '&filter_one_sheet=' . urlencode(html_entity_decode($this->request->get['filter_one_sheet'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_second_sheet'])) {
            $url .= '&filter_second_sheet=' . urlencode(html_entity_decode($this->request->get['filter_second_sheet'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }

        if (isset($this->request->get['sort'])) {
            $url .= '&sort=' . $this->request->get['sort'];
        }


        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }


        if (!isset($this->request->get['sheet_id'])) {
            $data['action'] = $this->url->link('admin/sheet/add', 'token=' . $this->session->data['token'] . $url);
        } else {
            $data['action'] = $this->url->link('admin/sheet/edit', 'token=' . $this->session->data['token'] . '&sheet_id=' . $this->request->get['sheet_id'] . $url);
        }

        $data['cancel'] = $this->url->link('admin/sheet/getList', 'token=' . $this->session->data['token'] . $url);

        if (isset($this->request->get['sheet_id'])) {
            $sheet_info = $this->model_admin_sheet->getSheetInfo($this->request->get['sheet_id']);
        }

        $data['type'] = isset($sheet_info) ?  $sheet_info['type'] : '';



        if (isset($this->request->post['name'])) {
            $data['name'] = $this->request->post['name'];
        } elseif (!empty($sheet_info)) {
            $data['name'] = $sheet_info['name'];
        } else {
            $data['name'] = '';
        }

        if (isset($sheet_info)) {
            if ($sheet_info['cate'] == 0) {
                $data['filter_second_sheet'] = '';
                $data['filter_second_sheet_name'] = '';
            } else {
                if ($sheet_info['type'] == 1) {
                    $data['filter_second_sheet'] = $sheet_info['sheet_id'];
                    $data['filter_second_sheet_name'] = $sheet_info['name'];
                } else {
                    $secondInfo = $this->model_admin_sheet->getSecondSheetByCate($sheet_info);
                    $data['filter_second_sheet'] = $secondInfo['sheet_id'];
                    $data['filter_second_sheet_name'] = $secondInfo['name'];
                }
            }

            if ($sheet_info['cate'] == 0) {
                $data['filter_one_sheet'] = $sheet_info['sheet_id'];    
                $data['filter_one_sheet_name'] = $sheet_info['name'];
                $data['filter_flow_id'] = $sheet_info['flow_id'];
            } else {
                $oneInfo = $this->model_admin_sheet->getOneSheetIdByCate($sheet_info);
                $data['filter_one_sheet'] = $oneInfo['sheet_id'];
                $data['filter_one_sheet_name'] = $oneInfo['name'];
                $data['filter_flow_id'] = $sheet_info['flow_id'];
            }
        } else {
            if (isset($this->request->post['filter_one_sheet']) && $this->request->post['filter_one_sheet']) {
                $data['filter_one_sheet'] = $this->request->post['filter_one_sheet'];
                $oneInfo = $this->model_admin_sheet->getSheetInfoById($this->request->post['filter_one_sheet']);
                $data['filter_one_sheet_name'] =  $oneInfo['name'];
                $data['filter_flow_id'] = 0;
            } else {
                $data['filter_one_sheet'] = '';
                $data['filter_one_sheet_name'] = '';
                $data['filter_flow_id'] = 0;
            }

            if (isset($this->request->post['filter_second_sheet']) && $this->request->post['filter_second_sheet']) {
                $data['filter_second_sheet'] = $this->request->post['filter_second_sheet'];
                $secondInfo = $this->model_admin_sheet->getSheetInfoById($this->request->post['filter_second_sheet']);
                $data['type'] = 2;
                $data['filter_second_sheet_name'] = $secondInfo['name'];
            }else {
                $data['second_sheet_id'] = '';
                $data['second_sheet_name'] = '';
                $data['filter_second_sheet'] = '';
            }

        }



        
        $data['flow_list'] = $this->model_admin_sheet->getFlowList();
        $data['sheetOneList'] = $this->model_admin_sheet->getOneSheet();
        $data['getSecondCate'] = $this->url->link('admin/sheet/getSecondCate', 'token=' . $this->session->data['token'] . '&noall=1');

        $data['header'] = $this->load->controller('admin/template/header'); 
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('sheet/sheet_form.tpl', $data));

    }

    protected function validateForm($sheet_id = 0)
    {
        $this->load->model('admin/sheet');
        if (!$this->user->hasPermission('modify', 'admin/sheet')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }
        

        if($this->request->post['sheet_type'] == 1){
            if(empty($this->request->post['flow_id'])){
                $this->error['warning'] = '请选择流程!';
                return false;
            }
        }



        if (empty($this->request->post['name'])) {
            $this->error['warning'] = '名称必填!';
            return false;
        }

        if (isset($this->request->post['filter_second_sheet']) && $this->request->post['filter_second_sheet'] != '') {
            $this->error['warning'] = '三级视图需要在前端页面添加/编辑人员添加!';
            return false;
        }

        $res = $this->model_admin_sheet->validateName($this->request->post, $sheet_id);

        if ($res != '200') {
            $this->error['warning'] = '名称重复!';
            return false;
        }


        return !$this->error;
    }

    protected function validateColumnForm($sheet_id = 0, $column_id = 0)
    {
        $this->load->model('admin/sheet');
        if (!$this->user->hasPermission('modify', 'admin/sheet')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }
        if (empty($this->request->post['name'])) {
            $this->error['warning'] = '名称必填!';
            return false;
        }
        if (empty($this->request->post['type'])) {
            $this->error['warning'] = '类型必选!';
            return false;
        }

        if($this->request->post['type'] == 'radio' || $this->request->post['type'] == 'check'){
            if(empty($this->request->post['radio_check'])){
                $this->error['warning'] = '没填写选择项!';
                return false;
            }
        }

        $res = $this->model_admin_sheet->validateColumnName($this->request->post, $sheet_id, $column_id);

        if ($res != '200') {
            $this->error['warning'] = '名称重复!';
            return false;
        }

        return !$this->error;
    }

    protected function validateDelete(array $sheet_ids)
    {
        if (!$this->user->hasPermission('modify', 'admin/sheet')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }

        $res = $this->model_admin_sheet->validateDel($sheet_ids);
        if ($res != '200') {
            $this->error['warning'] = '二级和三级至少要保留一项!';
            return false;
        }

        return !$this->error;
    }

    protected function validateColumnDelete($sheet_id,$column_ids)
    {
        if (!$this->user->hasPermission('modify', 'admin/sheet')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }
        $res = $this->model_admin_sheet->validateColumnDel($sheet_id,$column_ids);
        if ($res != '200') {
            if($res == 300){
                $this->error['warning'] = '特殊字段"发起人","执行人员","标题","完成进度"不能删除!';
            }else{
                $this->error['warning'] = '至少要保留一个字段!';
            }
            return false;
        }
        return !$this->error;
    }

    protected function validateGroupUser($data)
    {
        $uniond_arr = $data['union_id'] ?? [];
        $name = $data['name'] ?? '';
        if (!$this->user->hasPermission('modify', 'admin/sheet')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }
        if(empty($uniond_arr)){
            $this->error['warning'] = '至少要选择一人';
        } 
        if(empty($name)){
            $this->error['warning'] = '名称不能为空';
        } 

        return !$this->error;
    }



    public function sheetColumn()
    {
        $this->load->model('admin/sheet');
        $this->load->model('admin/sheet');

        if (isset($this->request->get['filter_name'])) {
            $filter_name = $this->request->get['filter_name'];
        } else {
            $filter_name = '';
        }

        if (isset($this->request->get['filter_one_sheet'])) {
            $filter_one_sheet = $this->request->get['filter_one_sheet'];
        } else {
            $filter_one_sheet = '';
        }

        if (isset($this->request->get['filter_second_sheet'])) {
            $filter_second_sheet = $this->request->get['filter_second_sheet'];
        } else {
            $filter_second_sheet = '';
        }

        if (isset($this->request->get['filter_date_start'])) {
            $filter_date_start = $this->request->get['filter_date_start'];
        } else {
            $filter_date_start = '';
        }

        if (isset($this->request->get['filter_date_end'])) {
            $filter_date_end = $this->request->get['filter_date_end'];
        } else {
            $filter_date_end = '';
        }

        if (isset($this->request->get['sort'])) {
            $sort = $this->request->get['sort'];
        } else {
            $sort = 'createTime';
        }

        if (isset($this->request->get['order'])) {
            $order = $this->request->get['order'];
        } else {
            $order = 'DESC';
        }


        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_one_sheet'])) {
            $url .= '&filter_one_sheet=' . urlencode(html_entity_decode($this->request->get['filter_one_sheet'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_second_sheet'])) {
            $url .= '&filter_second_sheet=' . urlencode(html_entity_decode($this->request->get['filter_second_sheet'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }

//        if (isset($this->request->get['sort'])) {
//            $url .= '&sort=' . $this->request->get['sort'];
//        }


        if ($order == 'ASC') {
            $url .= '&order=DESC';
        } else {
            $url .= '&order=ASC';
        }



        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        $data['add'] = $this->url->link('admin/sheet/column_add', 'token=' . $this->session->data['token'] . $url);
        $data['delete'] = $this->url->link('admin/sheet/column_delete', 'token=' . $this->session->data['token'] . $url);
        $data['getSecondCate'] = $this->url->link('admin/sheet/column_getSecondCate', 'token=' . $this->session->data['token']);


        $data['sheetOneList'] = $this->model_admin_sheet->getOneSheet();


        $filter_data = array(
            'filter_name' => $filter_name,
            'filter_one_sheet' => $filter_one_sheet,
            'filter_second_sheet' => $filter_second_sheet,
            'filter_date_start' => $filter_date_start,
            'filter_date_end' => $filter_date_end,
            'sort' => $sort,
            'order'=> $order,
            'start' => ($page - 1) * $this->config->get('config_limit'),
            'limit' => $this->config->get('config_limit')
        );


        $results = $this->model_admin_sheet->getSheets($filter_data, 1);

        foreach ($results as $k => $result) {
            $data['sheet'][$k] = array(
                'id' => $result['sheet_id'],
                'name' => $result['name'],
                'entry' => date('Y-m-d H:i:s', $result['createTime']),
                'is_one' => $result['cate'] == 0 ? '是' : '',
                'is_two' => $result['type'] == 1 ? '是' : '',
                'is_three' => $result['type'] == 2 ? '是' : '',
                'edit' => $this->url->link('admin/sheet/sheetColumn_column_list_detail', 'token=' . $this->session->data['token'] . '&sheet_id=' . $result['sheet_id'] . $url),
            );
        }
        $total = $this->model_admin_sheet->getTotalSheets($filter_data,1);

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }

        if (isset($this->session->data['success'])) {
            $data['success'] = $this->session->data['success'];

            unset($this->session->data['success']);
        } else {
            $data['success'] = '';
        }

        $data['sort_name'] = $this->url->link('admin/sheet/sheetColumn', 'token=' . $this->session->data['token'] . '&sort=name' . $url);
        $data['sort_createTime'] = $this->url->link('admin/sheet/sheetColumn', 'token=' . $this->session->data['token'] . '&sort=createTime' . $url);
        $data['filter_one_sheet'] = $filter_one_sheet;

        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('admin/sheet/sheetColumn_column_list_detail', 'token=' . $this->session->data['token'] . $url . '&page={page}');

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $data['filter_name'] = $filter_name;
        $data['filter_date_start'] = $filter_date_start;
        $data['filter_date_end'] = $filter_date_end;

        $data['nofilter'] = $this->url->link('admin/sheet/sheetColumn', 'token=' . $this->session->data['token']);

        $data['sort'] = $sort;
        $data['order'] = $order;

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('sheet/column_list.tpl', $data));
    }


    public function sheetColumn_column_list_detail()
    {
        $this->load->model('admin/sheet');
        $this->load->model('admin/sheet');
        $sheet_id = $this->request->get['sheet_id'];

        if (isset($this->request->get['filter_name'])) {
            $filter_name = $this->request->get['filter_name'];
        } else {
            $filter_name = '';
        }

        if (isset($this->request->get['filter_date_start'])) {
            $filter_date_start = $this->request->get['filter_date_start'];
        } else {
            $filter_date_start = '';
        }

        if (isset($this->request->get['filter_date_end'])) {
            $filter_date_end = $this->request->get['filter_date_end'];
        } else {
            $filter_date_end = '';
        }

        if (isset($this->request->get['sort'])) {
            $sort = $this->request->get['sort'];
        } else {
            $sort = 'createTime';
        }


        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $url = '';

        if (isset($this->request->get['sheet_id'])) {
            $url .= '&sheet_id=' . urlencode(html_entity_decode($this->request->get['sheet_id'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }

        if (isset($this->request->get['sort'])) {
            $url .= '&sort=' . $this->request->get['sort'];
        }

        if (isset($this->request->get['order'])) {
            $order = $this->request->get['order'];
        } else {
            $order = 'DESC';
        }

        if ($order == 'ASC') {
            $url .= '&order=DESC';
        } else {
            $url .= '&order=ASC';
        }


        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }


        $data['add'] = $this->url->link('admin/sheet/sheetColumn_column_add', 'token=' . $this->session->data['token'] . '&sheet_id=' . $sheet_id . $url);
        $data['delete'] = $this->url->link('admin/sheet/sheetColumn_column_delete', 'token=' . $this->session->data['token'] . $url);
        $data['getSecondCate'] = $this->url->link('admin/sheet/column_getSecondCate', 'token=' . $this->session->data['token']);


        $data['sheetOneList'] = $this->model_admin_sheet->getOneSheet();


        $filter_data = array(
            'filter_name' => $filter_name,
            'filter_date_start' => $filter_date_start,
            'filter_date_end' => $filter_date_end,
            'sort'              => $sort,
            'order'             => $order,
            'start' => ($page - 1) * $this->config->get('config_limit'),
            'limit' => $this->config->get('config_limit')
        );


        $results = $this->model_admin_sheet->getColumns($filter_data, $sheet_id);
        $data['type'] = $this->type;

        foreach ($results as $k => $result) {
            $data['column'][$k] = array(
                'id' => $result['column_id'],
                'name' => $result['name'],
                'sort' => $result['sort'],
                'entry' => date('Y-m-d H:i:s', $result['createTime']),
                'type' => $this->type[$result['type']],
                'edit' => $this->url->link('admin/sheet/sheetColumn_column_edit', 'token=' . $this->session->data['token'] . '&column_id=' . $result['column_id'] . $url),
            );
        }


        $total = $this->model_admin_sheet->getTotalColumns($filter_data, $sheet_id);

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }

        if (isset($this->session->data['success'])) {
            $data['success'] = $this->session->data['success'];

            unset($this->session->data['success']);
        } else {
            $data['success'] = '';
        }

        $url = '';

        if (isset($this->request->get['sheet_id'])) {
            $url .= '&sheet_id=' . urlencode(html_entity_decode($this->request->get['sheet_id'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }

        if ($order == 'ASC') {
            $url .= '&order=DESC';
        } else {
            $url .= '&order=ASC';
        }


        // if (isset($this->request->get['page'])) {
        //     $url .= '&page=' . $this->request->get['page'];
        // }


        $data['sort_name'] = $this->url->link('admin/sheet/sheetColumn_column_list_detail', 'token=' . $this->session->data['token'] . '&sort=name' . $url);
        $data['sort_createTime'] = $this->url->link('admin/sheet/sheetColumn_column_list_detail', 'token=' . $this->session->data['token'] . '&sort=createTime' . $url);
        $data['sort_sort'] = $this->url->link('admin/sheet/sheetColumn_column_list_detail', 'token=' . $this->session->data['token'] . '&sort=sort' . $url);
        $data['sort_type'] = $this->url->link('admin/sheet/sheetColumn_column_list_detail', 'token=' . $this->session->data['token'] . '&sort=' . $url);


        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('admin/sheet/getList', 'token=' . $this->session->data['token'] . $url . '&page={page}');

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $data['filter_name'] = $filter_name;
        $data['filter_date_start'] = $filter_date_start;
        $data['filter_date_end'] = $filter_date_end;

        $data['nofilter'] = $this->url->link('admin/sheet/sheetColumn_column_list_detail', 'token=' . $this->session->data['token'] . '&sheet_id=' . $sheet_id);

        $data['sort'] = $sort;
        $data['order'] = $order;

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');
        $this->response->setOutput($this->load->view('sheet/sheetColumn_column_list_detail.tpl', $data));
    }

    public function sheetColumn_column_add()
    {
        $this->load->model('admin/sheet');
        $sheet_id = $this->request->get['sheet_id'];
        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateColumnForm($sheet_id)) {
            $this->model_admin_sheet->addColumn($this->request->post, $sheet_id);

            $this->session->data['success'] = '添加成功';

            $url = '';
            if (isset($this->request->get['sheet_id'])) {
                $url .= '&sheet_id=' . urlencode(html_entity_decode($this->request->get['sheet_id'], ENT_QUOTES, 'UTF-8'));
            }


            if (isset($this->request->get['filter_name'])) {
                $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
            }

            if (isset($this->request->get['filter_date_start'])) {
                $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
            }

            if (isset($this->request->get['filter_date_end'])) {
                $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
            }

            if (isset($this->request->get['sort'])) {
                $url .= '&sort=' . $this->request->get['sort'];
            }


            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/sheet/sheetColumn_column_list_detail', 'token=' . $this->session->data['token'] . $url));
        }

        $this->getColumnForm();
    }

    public function sheetColumn_column_edit()
    {
        $this->load->model('admin/sheet');
        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateColumnForm($this->request->get['sheet_id'], $this->request->get['column_id'])) {
            $this->model_admin_sheet->editColumn($this->request->get['column_id'], $this->request->post,$this->request->get['sheet_id']);


            $this->session->data['success'] = $this->language->get('编辑成功');

            $url = '';

            if (isset($this->request->get['sheet_id'])) {
                $url .= '&sheet_id=' . urlencode(html_entity_decode($this->request->get['sheet_id'], ENT_QUOTES, 'UTF-8'));
            }

            if (isset($this->request->get['filter_name'])) {
                $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
            }


            if (isset($this->request->get['filter_date_start'])) {
                $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
            }

            if (isset($this->request->get['filter_date_end'])) {
                $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
            }

            if (isset($this->request->get['sort'])) {
                $url .= '&sort=' . $this->request->get['sort'];
            }


            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/sheet/sheetColumn_column_list_detail', 'token=' . $this->session->data['token'] . $url));
        }

        $this->getColumnForm();
    }

    public function sheetColumn_column_delete()
    {
        $this->load->model('admin/sheet');

        if (isset($this->request->post['selected']) && $this->validateColumnDelete($this->request->get['sheet_id'],$this->request->post['selected'])) {
            foreach ($this->request->post['selected'] as $column_id) {
                $this->model_admin_sheet->deleteColumn($column_id);
            }

            $this->session->data['success'] = $this->language->get('text_delete_success');

            $url = '';
            if (isset($this->request->get['sheet_id'])) {
                $url .= '&sheet_id=' . urlencode(html_entity_decode($this->request->get['sheet_id'], ENT_QUOTES, 'UTF-8'));
            }


            if (isset($this->request->get['filter_name'])) {
                $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
            }

            if (isset($this->request->get['filter_date_start'])) {
                $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
            }

            if (isset($this->request->get['filter_date_end'])) {
                $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
            }

            if (isset($this->request->get['sort'])) {
                $url .= '&sort=' . $this->request->get['sort'];
            }


            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/sheet/sheetColumn_column_list_detail', 'token=' . $this->session->data['token'] . $url));
        }

        $this->sheetColumn_column_list_detail();
    }


    protected function getColumnForm()
    {
        $this->load->model('admin/sheet');
        $data['text_form'] = !isset($this->request->get['column_id']) ? $this->language->get('text_add') : $this->language->get('text_edit');

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }
        if (isset($this->request->get['sheet_id'])) {
            $url .= '&sheet_id=' . urlencode(html_entity_decode($this->request->get['sheet_id'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }

        if (isset($this->request->get['sort'])) {
            $url .= '&sort=' . $this->request->get['sort'];
        }


        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }


        if (!isset($this->request->get['column_id'])) {
            $data['action'] = $this->url->link('admin/sheet/sheetColumn_column_add', 'token=' . $this->session->data['token'] . $url);
        } else {
            $data['action'] = $this->url->link('admin/sheet/sheetColumn_column_edit', 'token=' . $this->session->data['token'] . '&column_id=' . $this->request->get['column_id'] . $url);
        }

        $data['cancel'] = $this->url->link('admin/sheet/sheetColumn_column_list_detail', 'token=' . $this->session->data['token'] . $url);

        if (isset($this->request->get['column_id']) && ($this->request->server['REQUEST_METHOD'] != 'POST')) {
            $column_info = $this->model_admin_sheet->getColumnInfo($this->request->get['column_id']);
        }


        if (isset($this->request->post['name'])) {
            $data['name'] = $this->request->post['name'];
        } elseif (!empty($column_info)) {
            $data['name'] = $column_info['name'];
        } else {
            $data['name'] = '';
        }
        

        if (isset($this->request->post['sort'])) {
            $data['sort'] = $this->request->post['sort'];
        } elseif (!empty($column_info)) {
            $data['sort'] = $column_info['sort'];
        } else {
            $data['sort'] = 0;
        }

        $typeList = $this->type;
        $data['typeList'] = $typeList;

        if (isset($this->request->post['type'])) {
            $data['type'] = $this->request->post['type'];
            $data['typeName'] = $typeList[$this->request->post['type']];

        } elseif (!empty($column_info)) {
            $data['type'] = $column_info['type'];
            $data['typeName'] = $typeList[$column_info['type']];

        } else {
            $data['type'] = '';
            $data['typeName'] = '';
        }

        if (isset($this->request->post['radio_check'])) {
            $data['radio_check'] = $this->request->post['radio_check'] ?? [];
        } elseif (!empty($column_info)) {
            if($column_info['type'] == 'radio' || $column_info['type'] == 'check'){
                $radioCheckList =  $this->model_admin_sheet->getRadioCheck($column_info['column_id']) ;
                $data['radio_check']=  array_column($radioCheckList,'title');
            }else{
                $data['radio_check'] = [];
            }
        } else {
            $data['radio_check'] = [];
        }

        $data['column_id'] = isset($this->request->get['column_id']) ?? 0;


        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');
        $this->response->setOutput($this->load->view('sheet/column_form.tpl', $data));

    }

    public function sheetKpi(){

       $this->load->model('admin/sheet');
        $this->load->model('admin/sheet');
        

        if (isset($this->request->get['filter_name'])) {
            $filter_name = $this->request->get['filter_name'];
        } else {
            $filter_name = '';
        }

        if (isset($this->request->get['filter_months'])) {
            $filter_months = $this->request->get['filter_months'];
        } else {
            $filter_months = date('Y', time()).'-'.date('m', time());
        }


        if (isset($this->request->get['sort'])) {
            $sort = $this->request->get['sort'];
        } else {
            $sort = 'score ';
        }

       

        if (isset($this->request->get['order'])) {
            $order = $this->request->get['order'];
        } else {
            $order = 'ASC';
        }


        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_months'])) {
            $url .= '&filter_months=' . urlencode(html_entity_decode($this->request->get['filter_months'], ENT_QUOTES, 'UTF-8'));
        }


		if (isset($this->request->get['sort'])) {
			$url .= '&sort=' . $this->request->get['sort'];
		}

		if (isset($this->request->get['order'])) {
			$url .= '&order=' . $this->request->get['order'];
		}


        if ($order == 'ASC') {
            $url .= '&order=DESC';
        } else {
            $url .= '&order=ASC';
        }

        if (isset($this->request->get['page'])) {
			$url .= '&page=' . $this->request->get['page'];
		}

        $data['add'] = $this->url->link('admin/sheet/add', 'token=' . $this->session->data['token'] . $url);
        $data['delete'] = $this->url->link('admin/sheet/delete', 'token=' . $this->session->data['token'] . $url);
        $data['getSecondCate'] = $this->url->link('admin/sheet/getSecondCate', 'token=' . $this->session->data['token']);


        $data['months'] = ['01','02','03','04','05','06','07','08','09','10','11','12'];
        $data['monthsInfo']  = $filter_months;

        $filter_data = array(
            'filter_name' => $filter_name,
            'filter_months' => $filter_months,
            'sort' => $sort,
            'order'=> $order,
            'start' => ($page - 1) * $this->config->get('config_limit'),
            'limit' => $this->config->get('config_limit'),
        );


        $data['list'] = array();

        $results = $this->model_admin_sheet->getSheetKpi($filter_data);
        

        foreach ($results as $k => $result) {
            $extra = strval($result['extra_info'] ?? 0);
            $score = strval($result['score'] ?? 0);

            $deduct_money = bcsub($extra,  bcmul($score, bcmul($extra, '0.01', 10), 10), 2); // extra - part2
            $extra_info_score =  bcsub($extra,$deduct_money, 2);

            
            $data['sheet'][$k] = array(
                'id' => $result['id'],
                'union_id' => $result['union_id'],
                'real_name' => $result['real_name'],
                'months' => $result['months'],
                'score' => $result['score'],
                'extra_info'=>!is_null($result['extra_info']) ? number_format($result['extra_info'],2) : '--',
                'deduct_money'=> !is_null($result['extra_info']) ? $deduct_money : '--',
                'extra_info_score'=> !is_null($result['extra_info']) ?$extra_info_score: '--',
                'detail' => $this->url->link('admin/sheet/sheetKpiDetail', 'token=' . $this->session->data['token'] . '&union_id=' . $result['union_id'] . '&months=' . $result['months'])
            );

        }


        $total = $this->model_admin_sheet->getSheetKpiTotal($filter_data);

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }

        if (isset($this->session->data['success'])) {
            $data['success'] = $this->session->data['success'];
            unset($this->session->data['success']);
        } else {
            $data['success'] = '';
        }

        $url = '';

        if ($order == 'ASC') {
			$url .= '&order=DESC';
		} else {
			$url .= '&order=ASC';
		}

        if (isset($this->request->get['page'])) {
			$url .= '&page=' . $this->request->get['page'];
		}

      
        
        $data['sort_name'] = $this->url->link('admin/sheet/sheetKpi', 'token=' . $this->session->data['token'] . '&sort=real_name' . $url);
        $data['sort_months'] = $this->url->link('admin/sheet/sheetKpi', 'token=' . $this->session->data['token'] . '&sort=months' . $url);
        $data['sort_score'] = $this->url->link('admin/sheet/sheetKpi', 'token=' . $this->session->data['token'] . '&sort=score' . $url);
        $data['sort_createTime'] = $this->url->link('admin/sheet/sheetKpi', 'token=' . $this->session->data['token'] . '&sort=createTime' . $url);
        $data['sort_extra_info'] = $this->url->link('admin/sheet/sheetKpi', 'token=' . $this->session->data['token'] . '&sort=extra_info' . $url);


        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_months'])) {
            $url .= '&filter_months=' . urlencode(html_entity_decode($this->request->get['filter_months'], ENT_QUOTES, 'UTF-8'));
        }else{
            $url .= "&filter_months=".date('Y-m',time());
        }
      
        if (isset($this->request->get['sort'])) {
			$url .= '&sort=' . $this->request->get['sort'];
		}

		if (isset($this->request->get['order'])) {
			$url .= '&order=' . $this->request->get['order'];
		}

        // if (isset($this->request->get['page'])) {
        //     $url .= '&page=' . $this->request->get['page'];
        // }

        $data['exportKpi'] = $this->url->link('admin/sheet/exportKpi', 'token=' . $this->session->data['token'] . $url);


        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('admin/sheet/sheetKpi', 'token=' . $this->session->data['token'] . $url . '&page={page}');

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));
        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));


        $data['filter_name'] = $filter_name;
        $data['filter_months'] = $filter_months;

        $data['nofilter'] = $this->url->link('admin/sheet/sheetKpi', 'token=' . $this->session->data['token']);

        $data['sort'] = $sort;
        $data['order'] = $order;

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');
        $this->response->setOutput($this->load->view('sheet/sheetKpi.tpl', $data));
    }


    public function exportKpi() {
        if (isset($this->request->get['filter_name'])) {
            $filter_name = $this->request->get['filter_name'];
        } else {
            $filter_name = '';
        }

        if (isset($this->request->get['filter_months'])) {
            $filter_months = $this->request->get['filter_months'];
        } else {
            $filter_months = date('m', time());
        }



        if (isset($this->request->get['sort'])) {
            $sort = $this->request->get['sort'];
        } else {
            $sort = 'score ';
        }

       

        if (isset($this->request->get['order'])) {
            $order = $this->request->get['order'];
        } else {
            $order = 'ASC';
        }


        $this->load->model('admin/sheet');


        $filter_data = array(
            'filter_name'		=> $filter_name,
            'filter_months'		=> $filter_months,
            'sort'			=> $sort,
            'order'			=> $order,
        );

        $data['search'] = 0;
        $this->load->model('admin/sheet');
        $lists = $this->model_admin_sheet->getKpiAlgo($filter_data);

        $export_data = array();
        $export_data[] = array('姓名', '月份', '绩效分数', '岗位津贴','扣款', '应发岗位津贴');
        foreach ($lists as $v) {
            $extra = strval($v['extra_info'] ?? 0);
            $score = strval($v['score'] ?? 0);

            $deduct_money = bcsub($extra,  bcmul($score, bcmul($extra, '0.01', 10), 10), 2); // extra - part2
            $extra_info_score =  bcsub($extra,$deduct_money, 2);

            $export_data[] = array(
                $v['real_name'],
                $filter_months ?? '',
                number_format($v['score'],2) ?? '--',
                $v['extra_info'],
                 !is_null($v['extra_info']) ? $deduct_money : '--',
                 !is_null($v['extra_info']) ?$extra_info_score: '--',
            );
        }
        



        if (!empty($export_data)) {
            $this->load->model('admin/excel');
            $this->model_admin_excel->export('待办事项'.$filter_months.'月绩效' . date('Y-m-d'), $export_data, array(2 => 'numbric', 3 => 'numbric', 4 => 'numbric', 5 => 'numbric'), '.xlsx');
        }

        $this->sheetKpi();
    }


    public function sheetKpiDetail(){

        $this->load->model('admin/sheet');
 

        if (isset($this->request->get['union_id'])) {
            $filter_id = $this->request->get['union_id'];
        } else {
            $filter_id = '';
        }
 
         if (isset($this->request->get['months'])) {
             $filter_months = $this->request->get['months'];
         } else {
             $filter_months = date('m', time());
         }

         if (isset($this->request->get['filter_date_start'])) {
            $filter_date_start = $this->request->get['filter_date_start'];
        } else {
            $filter_date_start = '';
        }

        if (isset($this->request->get['filter_date_end'])) {
            $filter_date_end = $this->request->get['filter_date_end'];
        } else {
            $filter_date_end = '';
        }

 
         if (isset($this->request->get['sort'])) {
             $sort = $this->request->get['sort'];
         } else {
             $sort = 'create_time';
         }
 
        
 
         if (isset($this->request->get['order'])) {
             $order = $this->request->get['order'];
         } else {
             $order = 'DESC';
         }
 
 
         if (isset($this->request->get['page'])) {
             $page = $this->request->get['page'];
         } else {
             $page = 1;
         }
 
         $url = '';
 
         if (isset($this->request->get['union_id'])) {
            $url .= '&union_id=' . $this->request->get['union_id'];
        } 
 
         if (isset($this->request->get['months'])) {
             $url .= '&months=' . $this->request->get['months'];
         } 

         if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }
 
 
         if (isset($this->request->get['sort'])) {
             $url .= '&sort=' . $this->request->get['sort'];
         }
 
         if ($order == 'ASC') {
             $url .= '&order=DESC';
         } else {
             $url .= '&order=ASC';
         }
 
         if (isset($this->request->get['page'])) {
             $url .= '&page=' . $this->request->get['page'];
         }
 
         $data['add'] = $this->url->link('admin/sheet/add', 'token=' . $this->session->data['token'] . $url);
         $data['delete'] = $this->url->link('admin/sheet/delete', 'token=' . $this->session->data['token'] . $url);
  
 
         $filter_data = array(
            'filter_union_id' => $filter_id,
            'filter_months' => $filter_months,
            'filter_date_start' => $filter_date_start,
            'filter_date_end' => $filter_date_end,
             'sort' => $sort,
             'order'=> $order,
             'start' => ($page - 1) * $this->config->get('config_limit'),
             'limit' => $this->config->get('config_limit'),
         );
 
 
         $data['list'] = array();
 
         $results = $this->model_admin_sheet->getSheetKpiDetail($filter_data);
         
 
         foreach ($results as $k => $result) {
             $data['sheet'][$k] = array(
                'id' => $result['id'],
                 'union_id' => $result['union_id'],
                 'real_name' => $result['real_name'],
                 'create_time' => date('Y-m-d H:i:s', $result['create_time']),
                 'deadline' => date('Y-m-d', $result['deadline']),
                 'remark' => $result['remark'],
                 'is_compensation' => isset($result['is_compensation']) ? $result['is_compensation'] : 0
             );
         }
 
         $total = $this->model_admin_sheet->getSheetKpiDetailTotal($filter_data);
 
         if (isset($this->error['warning'])) {
             $data['warning'] = $this->error['warning'];
         } else {
             $data['warning'] = '';
         }
 
         if (isset($this->session->data['success'])) {
             $data['success'] = $this->session->data['success'];
             unset($this->session->data['success']);
         } else {
             $data['success'] = '';
         }
 
         $url = '';

 
         if (isset($this->request->get['union_id'])) {
            $url .= '&union_id=' . $this->request->get['union_id'];
        } 
 
         if (isset($this->request->get['months'])) {
             $url .= '&months=' . $this->request->get['months'];
         } 

         if (isset($this->request->get['filter_name'])) {
             $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
         }
 
         if (isset($this->request->get['filter_months'])) {
             $url .= '&filter_months=' . urlencode(html_entity_decode($this->request->get['filter_months'], ENT_QUOTES, 'UTF-8'));
         }

         if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }
 
       
 
         if ($order == 'ASC') {
             $url .= '&order=DESC';
         } else {
             $url .= '&order=ASC';
         }
 
         if (isset($this->request->get['page'])) {
             $url .= '&page=' . $this->request->get['page'];
         }
 
         
 
         $data['sort_name'] = $this->url->link('admin/sheet/sheetKpiDetail', 'token=' . $this->session->data['token'] . '&sort=name' . $url);
         $data['sort_months'] = $this->url->link('admin/sheet/sheetKpiDetail', 'token=' . $this->session->data['token'] . '&sort=months' . $url);
         $data['sort_score'] = $this->url->link('admin/sheet/sheetKpiDetail', 'token=' . $this->session->data['token'] . '&sort=score' . $url);
         $data['sort_create_time'] = $this->url->link('admin/sheet/sheetKpiDetail', 'token=' . $this->session->data['token'] . '&sort=create_time' . $url);
         
        $data['filter_date_start'] = $filter_date_start;
        $data['filter_date_end'] = $filter_date_end;
        $data['action_compensation'] = $this->url->link('admin/sheet/compensationScore', 'token=' . $this->session->data['token'] . $url);
 
 
         $pagination = new Pagination();
         $pagination->total = $total;
         $pagination->page = $page;
         $pagination->limit = $this->config->get('config_limit');
         $pagination->url = $this->url->link('admin/sheet/sheetKpiDetail', 'token=' . $this->session->data['token'] . $url . '&page={page}');
 
         $pagination_template = $this->load->view('common/pagination.tpl');
         $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));
 
         $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));
 
 
         $data['nofilter'] = $this->url->link('admin/sheet/sheetKpiDetail', 'token=' . $this->session->data['token'] . '&union_id=' . $filter_id . '&months=' . $filter_months);
 
         $data['sort'] = $sort;
         $data['order'] = $order;
 
         $data['header'] = $this->load->controller('admin/template/header');
         $data['content_top'] = $this->load->controller('admin/template/top');
         $data['content_bottom'] = $this->load->controller('admin/template/bottom');
         $data['footer'] = $this->load->controller('admin/template/footer');
 
         $this->response->setOutput($this->load->view('sheet/sheetKpiDetail.tpl', $data));
     }


     public function admin_add_edit()
    {
        $this->load->model('admin/sheet');
        if (($this->request->server['REQUEST_METHOD'] == 'POST')) {
            $user_id  = isset($this->session->data['union_id']) ? $this->session->data['union_id'] : 0;
            $this->model_admin_sheet->addEditUserAdmin($this->request->post,$user_id);

            $this->session->data['success'] = $this->language->get('text_edit_success');

            $url = '';

            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/sheet/getList', 'token=' . $this->session->data['token'] . $url));
        }

        $data['text_form'] = '超级管理员';

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }

        $url = '';

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

       
        $data['action'] = $this->url->link('admin/sheet/admin_add_edit', 'token=' . $this->session->data['token'] .  $url);
        $data['cancel'] = $this->url->link('admin/sheet/getList', 'token=' . $this->session->data['token'] . $url);

     
        $data['users'] = $this->model_admin_sheet->getUser();
        $userAdmin  = $this->model_admin_sheet->getUserAdmin();

        if($userAdmin){
            $data['union_id'] = array_column($userAdmin, 'union_id');
        }else{
            $data['union_id'] = array();
        }
        
        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('sheet/user_admin.tpl', $data));

    }


    public function sheetGroupUser(){
        $this->load->model('admin/sheet');
        $this->load->model('admin/sheet');

        if (isset($this->request->get['filter_name'])) {
            $filter_name = $this->request->get['filter_name'];
        } else {
            $filter_name = '';
        }



        if (isset($this->request->get['sort'])) {
            $sort = $this->request->get['sort'];
        } else {
            $sort = 'score asc';
        }

       

        if (isset($this->request->get['order'])) {
            $order = $this->request->get['order'];
        } else {
            $order = 'ASC';
        }


        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

 

		if (isset($this->request->get['sort'])) {
			$url .= '&sort=' . $this->request->get['sort'];
		}

		if (isset($this->request->get['order'])) {
			$url .= '&order=' . $this->request->get['order'];
		}


        if ($order == 'ASC') {
            $url .= '&order=DESC';
        } else {
            $url .= '&order=ASC';
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        $data['add'] = $this->url->link('admin/sheet/sheetGroupUserAdd', 'token=' . $this->session->data['token'] . $url);
        $data['delete'] = $this->url->link('admin/sheet/sheetGroupUserDelete', 'token=' . $this->session->data['token'] . $url);



        $filter_data = array(
            'filter_name' => $filter_name,
            'sort' => $sort,
            'order'=> $order,
            'start' => ($page - 1) * $this->config->get('config_limit'),
            'limit' => $this->config->get('config_limit'),
        );


        $data['list'] = array();

        $results = $this->model_admin_sheet->getSheetGroupUser($filter_data);
        

        foreach ($results as $k => $result) {
            $data['sheet'][$k] = array(
                'id' => $result['id'],
                'name' => $result['name'],
                'union_id' => $result['union_id'],
                'user_name' => $result['user_name'],
                'create_time' =>date('Y-m-d H:i:s',$result['create_time']),
                'edit' => $this->url->link('admin/sheet/sheetGroupUserAdd', 'token=' . $this->session->data['token'] . '&id=' . $result['id'])
            );
        }


        $total = $this->model_admin_sheet->getSheetGroupUserTotal($filter_data);

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }

        if (isset($this->session->data['success'])) {
            $data['success'] = $this->session->data['success'];
            unset($this->session->data['success']);
        } else {
            $data['success'] = '';
        }

        $url = '';

        if ($order == 'ASC') {
			$url .= '&order=DESC';
		} else {
			$url .= '&order=ASC';
		}

        if (isset($this->request->get['page'])) {
			$url .= '&page=' . $this->request->get['page'];
		}

      
        
        $data['sort_name'] = $this->url->link('admin/sheet/sheetGroupUser', 'token=' . $this->session->data['token'] . '&sort=name' . $url);
        $data['sort_user_name'] = $this->url->link('admin/sheet/sheetGroupUser', 'token=' . $this->session->data['token'] . '&sort=user_name' . $url);
        $data['sort_create_time'] = $this->url->link('admin/sheet/sheetGroupUser', 'token=' . $this->session->data['token'] . '&sort=create_time' . $url);
        $data['sort_id'] = $this->url->link('admin/sheet/sheetGroupUser', 'token=' . $this->session->data['token'] . '&sort=id' . $url);



        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }


      
        if (isset($this->request->get['sort'])) {
			$url .= '&sort=' . $this->request->get['sort'];
		}

		if (isset($this->request->get['order'])) {
			$url .= '&order=' . $this->request->get['order'];
		}

        // if (isset($this->request->get['page'])) {
        //     $url .= '&page=' . $this->request->get['page'];
        // }


        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit');
        $pagination->url = $this->url->link('admin/sheet/sheetGroupUser', 'token=' . $this->session->data['token'] . $url . '&page={page}');

        $pagination_template = $this->load->view('common/pagination.tpl');
        $data['pagination'] = $pagination->render((array)json_decode($pagination_template, true));

        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($page - 1) * $this->config->get('config_limit')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit')) > ($total - $this->config->get('config_limit'))) ? $total : ((($page - 1) * $this->config->get('config_limit')) + $this->config->get('config_limit')), $total, ceil($total / $this->config->get('config_limit')));

        $data['filter_name'] = $filter_name;

        $data['nofilter'] = $this->url->link('admin/sheet/sheetGroupUser', 'token=' . $this->session->data['token']);

        $data['sort'] = $sort;
        $data['order'] = $order;

        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');

        $this->response->setOutput($this->load->view('sheet/sheetGroupUser.tpl', $data));
    }


    public function sheetGroupUserAdd(){
     
        $this->load->model('admin/sheet');
        if (($this->request->server['REQUEST_METHOD'] == 'POST') && ($this->validateGroupUser($this->request->post))) {

            $this->model_admin_sheet->addEditGroupUser($this->request->post);

            if($this->request->post['id']){
                $this->session->data['success'] = $this->language->get('text_edit_success');
            }else{
                $this->session->data['success'] = $this->language->get('text_add_success');
            }

            $url = '';

            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('admin/sheet/sheetGroupUser', 'token=' . $this->session->data['token'] . $url));
        }

        $data['text_form'] = '用户群组添加';

        if (isset($this->error['warning'])) {
            $data['warning'] = $this->error['warning'];
        } else {
            $data['warning'] = '';
        }

        $url = '';

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

       
        $data['action'] = $this->url->link('admin/sheet/sheetGroupUserAdd', 'token=' . $this->session->data['token'] .  $url);
        $data['cancel'] = $this->url->link('admin/sheet/sheetGroupUser', 'token=' . $this->session->data['token'] . $url);

        $data['id'] = isset($this->request->get['id']) ?$this->request->get['id'] : 0;
     
        $data['users'] = $this->model_admin_sheet->getUser();
        $user  = $this->model_admin_sheet->getUnionGroup($data['id']);
        if($user){
            $data['union_id'] = json_decode($user['union_id'],true);
        }else{
            $data['union_id'] = array();
        }

        $data['name'] = isset($user['name']) ? $user['name'] : '';
        
        
        
        
        $data['header'] = $this->load->controller('admin/template/header');
        $data['content_top'] = $this->load->controller('admin/template/top');
        $data['content_bottom'] = $this->load->controller('admin/template/bottom');
        $data['footer'] = $this->load->controller('admin/template/footer');
        $this->response->setOutput($this->load->view('sheet/sheetGroupUserAdd.tpl', $data));

        
    }

    public function sheetGroupUserDelete()
    {
        $this->load->model('admin/sheet');
        if (isset($this->request->post['selected'])) {
            foreach ($this->request->post['selected'] as $id) {
                $this->model_admin_sheet->deleteGroupUser($id);
            }


            $this->session->data['success'] = $this->language->get('text_delete_success');

            $url = '';

            if (isset($this->request->get['filter_name'])) {
                $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
            }
            if (isset($this->request->get['sort'])) {
                $url .= '&sort=' . $this->request->get['sort'];
            }
    
            if (isset($this->request->get['order'])) {
                $url .= '&order=' . $this->request->get['order'];
            }
    
            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }
            $this->response->redirect($this->url->link('admin/sheet/sheetGroupUser', 'token=' . $this->session->data['token'] . $url));
        }

        $this->getList();
    }


    // public function retroactive(){
    //     $this->load->model('admin/sheet');
    //     $data['list'] = $this->model_admin_sheet->getSheetKpiDetail();
    //     $this->response->setOutput($this->load->view('sheet/sheetKpiDetail.tpl', $data));
    // }

    public function compensationScore() {
        $json = array();

        $date = $this->request->get['months'];
        $union_id = $this->request->get['union_id'];

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && isset($this->request->post['id'])) {
            // if (!$this->user->hasPermission('modify', 'admin/sheet/compensationScore')) {
            //     $this->error['warning'] = $this->language->get('error_permission');
            //     return;
            // }
            $this->load->model('admin/sheet');
            $info = $this->model_admin_sheet->findCompensationStatus($this->request->post['id']);
            if($info['is_compensation'] == 1){
                $json['error'] = '该扣分已失效，请刷新页面！';
                $this->response->addHeader('Content-Type: application/json');
                $this->response->setOutput(json_encode($json));
                return;
            }
            
            $id = (int)$this->request->post['id'];
            
            // 更新补分状态
            $result = $this->model_admin_sheet->updateCompensationStatus($id);

            //加分
            $result = $this->model_admin_sheet->updateCompensationScore($date,$union_id);
            
            
            if ($result) {
                $json['success'] = '补分操作成功！';
            } else {
                $json['error'] = '补分操作失败，请稍后重试！';
            }
        } else {
            $json['error'] = '无效的请求参数！';
        }
            
    
    
        
        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }
}
